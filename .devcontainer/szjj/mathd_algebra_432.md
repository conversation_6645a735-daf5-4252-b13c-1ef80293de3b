# Proof Tree for mathd_algebra_432

## Problem Statement
Expand (x + 3)(2x − 6) and verify that it equals 2x² − 18.

## Proof Tree

### ROOT_001 [ROOT]
**Goal**: Prove that (x + 3)(2x − 6) = 2x² − 18 for all real x
**Status**: [PROVEN]
**Proof Completion**: Successfully completed via STRATEGY_001 (algebraic expansion using distributive law)
**Children**: STRATEGY_001

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Strategy**: Algebraic expansion using distributive law (FOIL method)
**Detailed Plan**:
1. Apply distributive law: (x + 3)(2x − 6) = x(2x − 6) + 3(2x − 6)
2. Expand each term: x(2x − 6) = 2x² − 6x and 3(2x − 6) = 6x − 18
3. Combine like terms: 2x² − 6x + 6x − 18 = 2x² − 18
4. Verify the final result
**Status**: [PROVEN]
**Proof Completion**: All subgoals completed successfully using distributive laws and ring arithmetic
**Children**: SUBGOAL_001, SUBGOAL_002, SUBGOAL_003, SUBGOAL_004

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Apply distributive law to get (x + 3)(2x − 6) = x(2x − 6) + 3(2x − 6)
**Strategy**: Use left distributive property of multiplication over addition
**Status**: [PROVEN]
**Proof Completion**: Used `rw [add_mul]` to apply the distributive law
**Mathlib Reference**: `add_mul` from `Mathlib.Algebra.Ring.Defs`

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Expand x(2x − 6) = 2x² − 6x
**Strategy**: Apply distributive law within the first term
**Status**: [PROVEN]
**Proof Completion**: Used `rw [mul_sub]` and `ring` to expand and simplify
**Mathlib Reference**: `mul_sub` from `Mathlib.Algebra.Ring.Defs` and `ring` tactic

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Expand 3(2x − 6) = 6x − 18
**Strategy**: Apply distributive law within the second term
**Status**: [PROVEN]
**Proof Completion**: Used `rw [mul_sub]` and `ring` to expand and simplify
**Mathlib Reference**: `mul_sub` from `Mathlib.Algebra.Ring.Defs` and `ring` tactic

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Combine like terms: 2x² − 6x + 6x − 18 = 2x² − 18
**Strategy**: Use additive inverse property to cancel −6x + 6x = 0
**Status**: [PROVEN]
**Proof Completion**: Used `ring` tactic for algebraic simplification
**Mathlib Reference**: `ring` tactic from `Mathlib.Tactic.Ring`
