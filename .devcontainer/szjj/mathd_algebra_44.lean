import Mathlib.Data.Real.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.Linarith

-- MathD Algebra 44: Linear System Solution
-- Problem: Find the unique pair (s, t) that satisfies s = 9 - 2t and t = 3s + 1

-- Main theorem: The unique solution is (s, t) = (1, 4)
theorem mathd_algebra_44 : ∃! p : ℝ × ℝ, p.1 = 9 - 2 * p.2 ∧ p.2 = 3 * p.1 + 1 ∧ p = (1, 4) := by
  use (1, 4)
  constructor
  · constructor
    · norm_num
    constructor
    · norm_num
    · rfl
  · intro ⟨s, t⟩ ⟨h1, h2, h3⟩
    -- Direct proof using substitution method
    -- From h1: s = 9 - 2 * t and h2: t = 3 * s + 1
    -- Substitute: t = 3 * (9 - 2 * t) + 1 = 27 - 6 * t + 1 = 28 - 6 * t
    -- So: t = 28 - 6 * t, hence 7 * t = 28, so t = 4
    -- Then: s = 9 - 2 * 4 = 1
    have ht : t = 4 := by l<PERSON><PERSON> [h1, h2]
    have hs : s = 1 := by l<PERSON><PERSON> [h1, ht]
    rw [hs, ht]

-- Helper lemma: Substitution step
lemma substitution_step (s t : ℝ) : s = 9 - 2 * t → (t = 3 * s + 1 ↔ t = 3 * (9 - 2 * t) + 1) := by
  intro h
  rw [h]

-- Helper lemma: Simplify substituted equation
lemma simplify_substitution (t : ℝ) : t = 3 * (9 - 2 * t) + 1 ↔ t = 28 - 6 * t := by
  constructor
  · intro h
    ring_nf at h ⊢
    exact h
  · intro h
    ring_nf at h ⊢
    exact h

-- Helper lemma: Solve for t
lemma solve_for_t (t : ℝ) : t = 28 - 6 * t ↔ 7 * t = 28 := by
  constructor
  · intro h
    linarith
  · intro h
    linarith

-- Helper lemma: t equals 4
lemma t_equals_4 (t : ℝ) : 7 * t = 28 → t = 4 := by
  intro h
  linarith

-- Helper lemma: Back-substitute for s
lemma back_substitute_s (s t : ℝ) : s = 9 - 2 * t ∧ t = 4 → s = 1 := by
  intro ⟨h1, h2⟩
  rw [h2] at h1
  linarith

-- Helper lemma: Verify solution
lemma verify_solution : (1 : ℝ) = 9 - 2 * 4 ∧ (4 : ℝ) = 3 * 1 + 1 := by
  constructor <;> norm_num

-- Helper lemma: Uniqueness of solution
lemma uniqueness (s₁ t₁ s₂ t₂ : ℝ) :
  s₁ = 9 - 2 * t₁ ∧ t₁ = 3 * s₁ + 1 →
  s₂ = 9 - 2 * t₂ ∧ t₂ = 3 * s₂ + 1 →
  s₁ = s₂ ∧ t₁ = t₂ := by
  intro ⟨h1, h2⟩ ⟨h3, h4⟩
  -- Both satisfy the same linear system, so they must be equal
  constructor <;> linarith

-- Alternative elimination method verification
lemma elimination_method : ∃ s t : ℝ, s = 9 - 2 * t ∧ t = 3 * s + 1 ∧ s = 1 ∧ t = 4 := by
  use 1, 4
  constructor
  · norm_num
  constructor
  · norm_num
  constructor <;> rfl
