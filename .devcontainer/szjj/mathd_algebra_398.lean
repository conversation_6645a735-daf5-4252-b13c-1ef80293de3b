-- Problem: Convert 80 lugs into ligs given that 7 ligs = 4 lags and 9 lags = 20 lugs.

import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Field.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum
import Mathlib.Tactic.LinearCombination

-- Main theorem: 80 lugs = 63 ligs given the conversion rates
theorem mathd_algebra_398 : 80 * ((9 : ℝ) / 20) * ((7 : ℝ) / 4) = 63 := by
  -- Direct computation using norm_num
  norm_num
