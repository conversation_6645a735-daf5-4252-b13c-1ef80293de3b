import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.LinearCombination

-- MATHD Algebra 427: Linear System Sum

theorem mathd_algebra_427 : ∃ x y z : ℚ,
  (3 * x + y = 17) ∧
  (5 * y + z = 14) ∧
  (3 * x + 5 * z = 41) ∧
  (x + y + z = 12) := by

  -- Step 2: Simplify left side by combining like terms
  have step2 : ∀ x y z : ℚ,
    (3 * x + y) + (5 * y + z) + (3 * x + 5 * z) = 6 * x + 6 * y + 6 * z := by
    intros x y z
    ring

  -- Step 3: Simplify right side by arithmetic
  have step3 : (17 : ℚ) + 14 + 41 = 72 := by
    norm_num

  -- Step 1: Add all three equations
  have step1 : ∀ x y z : ℚ,
    (3 * x + y = 17) → (5 * y + z = 14) → (3 * x + 5 * z = 41) →
    (6 * x + 6 * y + 6 * z = 72) := by
    intros x y z h1 h2 h3
    have h_left := step2 x y z
    rw [← h_left]
    rw [h1, h2, h3]
    exact step3

  -- Step 4: Factor out common coefficient
  have step4 : ∀ x y z : ℚ,
    6 * x + 6 * y + 6 * z = 6 * (x + y + z) := by
    intros x y z
    ring

  -- Step 5: Solve for the sum
  have step5 : ∀ x y z : ℚ,
    6 * (x + y + z) = 72 → x + y + z = 12 := by
    intros x y z h
    linarith

  -- Step 6: Alternative verification using elimination method
  have step6 : ∃ x y z : ℚ,
    x = 66/13 ∧ y = 23/13 ∧ z = 67/13 ∧
    (3 * x + y = 17) ∧ (5 * y + z = 14) ∧ (3 * x + 5 * z = 41) := by
    use 66/13, 23/13, 67/13
    constructor
    · rfl
    constructor
    · rfl
    constructor
    · rfl
    constructor
    · norm_num
    constructor
    · norm_num
    · norm_num

  -- Step 7: Verify individual sum equals 12
  have step7 : (66 : ℚ)/13 + 23/13 + 67/13 = 12 := by
    norm_num

  -- Step 8: Main proof using direct method
  use 66/13, 23/13, 67/13
  constructor
  · -- Verify first equation: 3x + y = 17
    norm_num
  constructor
  · -- Verify second equation: 5y + z = 14
    norm_num
  constructor
  · -- Verify third equation: 3x + 5z = 41
    norm_num
  · -- Show sum equals 12
    exact step7
