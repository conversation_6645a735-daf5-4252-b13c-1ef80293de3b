-- Problem: Expand (x + 3)(2x − 6) and verify that it equals 2x² − 18.

import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Ring.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum
import Mathlib.Tactic.LinearCombination

-- Main theorem: (x + 3)(2x − 6) = 2x² − 18 for all real x
theorem mathd_algebra_432 (x : ℝ) : (x + 3) * (2 * x - 6) = 2 * x^2 - 18 := by
  -- Strategy: Algebraic expansion using distributive law (FOIL method)
  -- Step 1: Apply distributive law
  have step1 : (x + 3) * (2 * x - 6) = x * (2 * x - 6) + 3 * (2 * x - 6) := by
    rw [add_mul]

  -- Step 2: Expand x(2x − 6) = 2x² − 6x
  have step2 : x * (2 * x - 6) = 2 * x^2 - 6 * x := by
    rw [mul_sub]
    ring

  -- Step 3: Expand 3(2x − 6) = 6x − 18
  have step3 : 3 * (2 * x - 6) = 6 * x - 18 := by
    rw [mul_sub]
    ring

  -- Step 4: Combine like terms
  have step4 : 2 * x^2 - 6 * x + (6 * x - 18) = 2 * x^2 - 18 := by
    ring

  -- Combine all steps
  rw [step1, step2, step3]
  exact step4
