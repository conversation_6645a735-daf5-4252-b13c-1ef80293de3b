# Proof Tree for mathd_algebra_354

## Problem Statement
Given a₇ = 30 and a₁₁ = 60 in an arithmetic sequence, find a₂₁.

## Proof Tree

### ROOT_001 [ROOT]
**Goal**: Prove that given a₇ = 30 and a₁₁ = 60 in an arithmetic sequence, a₂₁ = 135
**Status**: [PROVEN]
**Proof Completion**: Successfully completed via STRATEGY_001 (arithmetic sequence formula and system of equations)
**Children**: STRATEGY_001

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Strategy**: Use arithmetic sequence formula and system of equations
**Detailed Plan**:
1. Use the arithmetic sequence formula aₙ = a₁ + (n-1)d
2. Set up equations for a₇ = a₁ + 6d = 30 and a₁₁ = a₁ + 10d = 60
3. Solve the system to find d and a₁
4. Calculate a₂₁ = a₁ + 20d
**Status**: [PROVEN]
**Proof Completion**: All subgoals completed successfully using formula definition, linear_combination, and numerical computation
**Children**: SUBGOAL_001, SUBG<PERSON>L_002, SUBGOAL_003, SUBGOAL_004

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Establish arithmetic sequence formula aₙ = a₁ + (n-1)d
**Strategy**: Use definition of arithmetic sequence
**Status**: [PROVEN]
**Proof Completion**: Used `intro n; rfl` since the formula is the definition itself
**Mathlib Reference**: Direct from definition

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Set up system of equations: a₁ + 6d = 30 and a₁ + 10d = 60
**Strategy**: Apply arithmetic sequence formula to given terms
**Status**: [PROVEN]
**Proof Completion**: Used `rw [formula]` and `norm_num` to simplify the arithmetic sequence expressions

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Solve system to find d = 7.5 and a₁ = -15
**Strategy**: Subtract equations to eliminate a₁, then substitute back
**Status**: [PROVEN]
**Proof Completion**: Used `linear_combination` to solve the linear system of equations

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Calculate a₂₁ = a₁ + 20d = -15 + 20(7.5) = 135
**Strategy**: Apply arithmetic sequence formula with found values
**Status**: [PROVEN]
**Proof Completion**: Used `linear_combination` with the found values to compute the result
