import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum
import Mathlib.Tactic.Linarith
import Mathlib.Data.Nat.Basic

-- MATHD Algebra 392: Three consecutive positive even integers with sum of squares 12296

theorem mathd_algebra_392 : ∃ a b c : ℕ,
  (∃ k : ℕ, k ≥ 2 ∧ a = 2*k - 2 ∧ b = 2*k ∧ c = 2*k + 2) ∧
  a^2 + b^2 + c^2 = 12296 ∧
  (a * b * c) / 8 = 32736 := by

  -- Direct computational approach: use a = 62, b = 64, c = 66
  use 62, 64, 66
  constructor
  · -- Show parametric representation with k = 32
    use 32
    constructor
    · norm_num  -- 32 ≥ 2
    constructor
    · norm_num  -- 62 = 2*32 - 2
    constructor
    · norm_num  -- 64 = 2*32
    · norm_num  -- 66 = 2*32 + 2
  constructor
  · -- Show sum of squares condition: 62² + 64² + 66² = 12296
    norm_num
  · -- Show product divided by 8: (62 * 64 * 66) / 8 = 32736
    norm_num
