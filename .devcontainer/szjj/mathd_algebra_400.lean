import Mathlib.Data.Real.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum
import Mathlib.Tactic.FieldSimp
import Mathlib.Tactic.Linarith

-- MathD Algebra 400: Find x such that 5 + 500% of 10 = 110% of x
theorem mathd_algebra_400 :
  ∃ (x : ℝ),
    5 + 5 * 10 = 1.1 * x ∧
    x = 50 := by

  -- Use x = 50
  use 50

  constructor
  -- Prove 5 + 5 * 10 = 1.1 * 50
  · -- Left side: 5 + 500% of 10 = 5 + 5 * 10 = 5 + 50 = 55
    -- Right side: 110% of 50 = 1.1 * 50 = 55
    have h_left : (5 : ℝ) + 5 * 10 = 55 := by
      -- Calculate: 5 + 5 * 10 = 5 + 50 = 55
      norm_num
    have h_right : (1.1 : ℝ) * 50 = 55 := by
      -- Calculate: 1.1 * 50 = 55
      norm_num
    rw [h_left, h_right]

  -- Prove x = 50
  · rfl
