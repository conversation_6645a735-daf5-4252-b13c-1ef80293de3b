# MathD Algebra 419 Proof Tree

## Problem Statement
Evaluate −a − b² + 3ab at a = −1 and b = 5.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that −a − b² + 3ab = −39 when a = −1 and b = 5
**Status**: [ROOT]
**Strategy**: Direct substitution and algebraic simplification

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Substitute the given values a = −1 and b = 5 directly into the expression −a − b² + 3ab and simplify step by step
**Strategy**: Direct substitution approach with step-by-step arithmetic
**Status**: [PROVEN]
**Proof Completion**: Successfully completed using norm_num for direct numerical computation

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Substitute a = −1 into −a term: −a = −(−1) = 1
**Strategy**: Direct substitution and sign simplification
**Status**: [PROVEN]
**Proof Completion**: Implicit in norm_num calculation

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Substitute b = 5 into −b² term: −b² = −5² = −25
**Strategy**: Direct substitution and power calculation
**Status**: [PROVEN]
**Proof Completion**: Implicit in norm_num calculation

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Substitute a = −1, b = 5 into 3ab term: 3ab = 3(−1)(5) = −15
**Strategy**: Direct substitution and multiplication
**Status**: [PROVEN]
**Proof Completion**: Implicit in norm_num calculation

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Combine all terms: 1 + (−25) + (−15) = 1 − 25 − 15 = −39
**Strategy**: Arithmetic addition and simplification
**Status**: [PROVEN]
**Proof Completion**: Implicit in norm_num calculation

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Verify the calculation using alternative approach: factor and compute
**Strategy**: Factor −a + 3ab = a(−1 + 3b) and verify result
**Status**: [PROVEN]
**Proof Completion**: Alternative verification implicit in norm_num calculation

### SUBGOAL_006 [SUBGOAL]
**Parent Node**: SUBGOAL_005
**Goal**: Factor common term: −a + 3ab = a(−1 + 3b) = (−1)(−1 + 3·5) = (−1)(14) = −14
**Strategy**: Algebraic factoring and substitution
**Status**: [PROVEN]
**Proof Completion**: Alternative verification implicit in norm_num calculation

### SUBGOAL_007 [SUBGOAL]
**Parent Node**: SUBGOAL_005
**Goal**: Add the remaining term: −14 + (−b²) = −14 + (−25) = −39
**Strategy**: Final arithmetic verification
**Status**: [PROVEN]
**Proof Completion**: Alternative verification implicit in norm_num calculation

### SUBGOAL_008 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Establish final result: −a − b² + 3ab = −39 when a = −1, b = 5
**Strategy**: Combine all computational steps into final conclusion
**Status**: [PROVEN]
**Proof Completion**: Final result established using norm_num for complete numerical verification
