-- Problem: Find the son's present age given that the father is now five times as old,
-- and their ages totaled 30 three years ago.

import Mathlib.Data.Nat.Basic
import Mathlib.Algebra.Ring.Basic
import Mathlib.Tactic

-- Define the main theorem
theorem son_age_is_six : ∃ (s f : ℕ), f = 5 * s ∧ (f - 3) + (s - 3) = 30 ∧ s = 6 := by
  -- SUBGOAL_001: Define variables and set up the problem
  use 6, 30
